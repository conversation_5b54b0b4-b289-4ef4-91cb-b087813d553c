<?php
if ($_SERVER["REQUEST_METHOD"] == "POST"){
    $name= htmlspecialchars($_POST["name"]);
    $music=htmlspecialchars($_POST["music"]);
    echo "<h2>Hi, $name!</h2>";
    echo "<p><b>Let's vibe to your beloved : $music music </b></p>";

    if ($music == "rock"){
        echo'        
        <audio id="audio" autoplay loop style = "display:none;">
        <source src="rock.mp3" type="audio/mpeg">
        </audio>';
        $bg_image="https://parade.com/.image/t_share/MTkwNTgxMjYyNzA0NzE1NjQ0/joan-jett-runaways.jpg";


    }
    elseif($music=="lofi"){
        echo 'music palying';
        echo'        <audio id="audio" autoplay loop style = "display:none;">
        <source src="lofi.mp3" type="audio/mpeg">
        </audio>';
        $bg_image="https://i.ytimg.com/vi/5qap5aO4i9A/maxresdefault.jpg";
        header("location:lofi.php");
        exit;

    }
    else{
        echo'<audio id="audio"  >
        <source src="r&b.mp3" type="audio/mpeg" style = "display:none;">
        </audio>
        ';
        $bg_image="https://i.scdn.co/image/ab67616d0000b2738e8b9052903082f05b4714b2";
    }

}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Submission Received</title>
</head>
<body style="background-image: url('<?php echo $bg_image; ?>');">
    <h1>Thank You!</h1>
<div>
<a href="index.php">
    <button>Go Back</button></a>
</div>
</body>
</html>

