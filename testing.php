
<?php 

// $num = 10;
// if
// ($num%2==0) {
// echo
// 'even';
// }else{ echo 'odd'; }
?>


<!-- Q3. Check if a string 'name' is empty in PHP.

<!-- <?php 

// if (empty($name)) {echo 'sth' ;}
?> -->

 -->

<?php
// $casevar=20;
// switch ($casevar){
// case ("10"):
//     echo'10';
// case ("20"):
//     echo '20';
// }
?>


<?php


// function square($num1,$num2){


//     echo $num1.' '.$num2;
// }

// square(2,3)
?>

<?php

?>

Q3. Add the element 'grape' to the end of the array $fruits.

<?php
array_push
($fruits, 'grape');
?>

Q4. Remove the first element from the array $numbers.

<?php
// array_shift
// ($numbers);
?>

last ko lai array_pop

<?php
$num1=1;
$num2="1";
var_dump($num1==$num2);
var_dump($num1===$num2);
?>